import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import type { BenchmarkConfig, AgentResult } from '../config/types';
import type { CommandExecutor } from '../utils/shell';
import type { Logger } from '../utils/logger';
import type { TestSuiteReader } from '../test-suites/reader';
import { AgentFactory } from '../agents/factory';
import { DockerExecutionStrategy } from '../execution/docker-strategy';
import { LocalExecutionStrategy } from '../execution/local-strategy';

export class TestSuiteAgentRunner {
    constructor(
        private executor: CommandExecutor,
        private testSuiteReader: TestSuiteReader,
        private logger: Logger,
        private containerName: string
    ) {}

    async run(
        config: BenchmarkConfig,
        suiteId: string,
        testCaseId: string,
        useDocker: boolean = true
    ): Promise<AgentResult> {
        const startTime = Date.now();
        
        try {
            // Load the test suite and find the specific test case
            this.logger.info(`Loading test suite: ${suiteId}`);
            const suite = await this.testSuiteReader.loadSuite(suiteId);
            
            const testCase = suite.tests.find(tc => tc.id === testCaseId);
            if (!testCase) {
                throw new Error(`Test case ${testCaseId} not found in suite ${suiteId}`);
            }

            // Map suite:testCase combination to exercise name for logging compatibility
            const exerciseName = `${suiteId}:${testCaseId}`;
            this.logger.info(`Running agent for test case: ${exerciseName}`);

            // Create agent builder for test suite execution
            const agentBuilder = AgentFactory.createForTestSuite(config, this.containerName);
            
            // Build command with test case prompt and system prompt
            const command = await agentBuilder.buildTestSuiteCommand(
                testCase.prompt,
                suite.system_prompt || ''
            );

            // Create temporary working directory for agent execution
            const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'test-suite-'));
            
            try {
                // Choose execution strategy
                const strategy = useDocker
                    ? new DockerExecutionStrategy(this.containerName)
                    : new LocalExecutionStrategy();

                // Prepare and execute command
                const preparedCommand = strategy.prepare(command, { exercisePath: tempDir, testFiles: [] });
                this.logger.info(`Executing agent command for ${exerciseName}`);
                
                const result = await this.executor.execute(preparedCommand.command, preparedCommand.options);
                const endTime = Date.now();

                return {
                    exercise: exerciseName,
                    success: result.exitCode === 0,
                    output: result.stdout,
                    error: result.stderr,
                    duration: endTime - startTime
                };

            } finally {
                // Clean up temporary directory
                try {
                    await fs.rm(tempDir, { recursive: true, force: true });
                } catch (cleanupError) {
                    this.logger.warn(`Failed to clean up temp directory ${tempDir}: ${cleanupError}`);
                }
            }

        } catch (error) {
            const endTime = Date.now();
            const exerciseName = `${suiteId}:${testCaseId}`;
            
            this.logger.error(`Agent execution failed for ${exerciseName}: ${error}`);
            
            return {
                exercise: exerciseName,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                duration: endTime - startTime
            };
        }
    }
}

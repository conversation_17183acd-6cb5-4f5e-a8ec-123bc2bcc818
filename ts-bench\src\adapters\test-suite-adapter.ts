import type { ExerciseReader } from '../exercises/reader';
import type { FileList } from '../agents/types';
import type { TestSuiteReader, LoadedSuite, LoadedTestCase } from '../test-suites/reader';

/**
 * Converts a test suite + test case into an exercise-like structure
 */
export class TestSuiteToExerciseAdapter {
    constructor(
        private suite: LoadedSuite,
        private testCase: LoadedTestCase
    ) {}

    /**
     * Converts test suite ID and test case ID to exercise name format
     */
    getExerciseName(): string {
        return `${this.suite.id}:${this.testCase.id}`;
    }

    /**
     * Combines system prompt and test case prompt into instructions
     */
    getInstructions(): string {
        const systemPrompt = this.suite.system_prompt || '';
        return systemPrompt ? `${systemPrompt}\n\n${this.testCase.prompt}` : this.testCase.prompt;
    }

    /**
     * Returns empty file list since test suites don't have files
     */
    getFileList(): FileList {
        return {
            sourceFiles: [],
            testFiles: []
        };
    }

    /**
     * Gets the expected answer for evaluation
     */
    getExpectedAnswer(): string[] {
        return this.testCase.answers;
    }
}

/**
 * ExerciseReader implementation that reads from test suites instead of file system
 */
export class TestSuiteExerciseReader implements ExerciseReader {
    constructor(private testSuiteReader: TestSuiteReader) {}

    async getInstructions(exerciseName: string): Promise<string> {
        const { suiteId, testCaseId } = this.parseExerciseName(exerciseName);
        const suite = await this.testSuiteReader.loadSuite(suiteId);
        const testCase = suite.tests.find(tc => tc.id === testCaseId);
        
        if (!testCase) {
            throw new Error(`Test case ${testCaseId} not found in suite ${suiteId}`);
        }

        const adapter = new TestSuiteToExerciseAdapter(suite, testCase);
        return adapter.getInstructions();
    }

    async getFileList(exerciseName: string): Promise<FileList> {
        // Test suites don't have files, so return empty arrays
        return {
            sourceFiles: [],
            testFiles: []
        };
    }

    async listExercises(): Promise<string[]> {
        // This would need to be implemented to list all available test cases
        // across all suites in the format suiteId:testCaseId
        throw new Error('listExercises not implemented for TestSuiteExerciseReader');
    }

    /**
     * Parses exercise name in format "suiteId:testCaseId" back to components
     */
    private parseExerciseName(exerciseName: string): { suiteId: string; testCaseId: string } {
        const parts = exerciseName.split(':');
        if (parts.length !== 2) {
            throw new Error(`Invalid exercise name format: ${exerciseName}. Expected format: suiteId:testCaseId`);
        }
        return {
            suiteId: parts[0],
            testCaseId: parts[1]
        };
    }
}

/**
 * Utility functions for converting between test suite and exercise formats
 */
export class TestSuiteAdapterUtils {
    /**
     * Converts suite ID and test case ID to exercise name
     */
    static toExerciseName(suiteId: string, testCaseId: string): string {
        return `${suiteId}:${testCaseId}`;
    }

    /**
     * Parses exercise name back to suite and test case IDs
     */
    static fromExerciseName(exerciseName: string): { suiteId: string; testCaseId: string } {
        const parts = exerciseName.split(':');
        if (parts.length !== 2) {
            throw new Error(`Invalid exercise name format: ${exerciseName}. Expected format: suiteId:testCaseId`);
        }
        return {
            suiteId: parts[0],
            testCaseId: parts[1]
        };
    }

    /**
     * Checks if an exercise name is in test suite format
     */
    static isTestSuiteExercise(exerciseName: string): boolean {
        return exerciseName.includes(':') && exerciseName.split(':').length === 2;
    }
}
